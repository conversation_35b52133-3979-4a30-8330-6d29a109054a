<template>
    <Panel v-model:collapsed="isCollapsed" :header="header" :toggleable="toggleable" pt:header:class="text-2xl">
        <template #toggleicon><span :class="isCollapsed ? Icon.EXPAND : Icon.COLLAPSE" /></template>
        <template #default><slot /></template>
        <template v-if="$slots.footer" #footer><slot name="footer" /></template>
    </Panel>
</template>

<script setup lang="ts">
    import { Panel } from 'primevue';
    import { Icon } from '~/utils/enums';

    const isCollapsed = defineModel<boolean>({ default: false });

    const { header, toggleable } = defineProps<{
        header: string;
        toggleable?: boolean;
    }>();
</script>
