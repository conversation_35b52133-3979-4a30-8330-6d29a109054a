<template>
    <div :class="['font-bold', `text-${size}`]"><slot>{{ headingValue }}</slot></div>
</template>

<script setup lang="ts">
    import { createOptional } from '@ti-platform/aide';
    import { computed } from 'vue';

    const { size, heading } = defineProps<{
        size: 'sm' | 'l' | 'xl';
        heading?: string;
    }>();

    const headingValue = computed(() => createOptional(heading).orElse(''));
</script>
