<template>
    <MultiSelectExtended
        v-model="model"
        :options="options"
        display="chip"
        option-label="display"
        option-value="value"
        toggle-all-label="Select All"
        fluid
        pt:header:class="border-b-2 pb-4"
    />
</template>

<script setup lang="ts">
    import { MultiSelectExtended } from '@ti-platform/aide-primevue';
    import type { SelectOption } from '~/utils/transformers';

    const model = defineModel<Array<string>>();

    const { options } = defineProps<{
        options: Array<SelectOption>;
    }>();
</script>
