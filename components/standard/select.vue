<template>
    <Select v-model="model" :options="selectOptions" option-label="display" option-value="value" fluid />
</template>

<script setup lang="ts">
    import { Select } from 'primevue';
    import { computed } from 'vue';
    import type { MapPlus } from '@ti-platform/aide';

    import { toSelectOptions } from '~/utils/transformers';

    const model = defineModel<string>();

    const { options } = defineProps<{
        options: MapPlus<string, string>;
    }>();

    const selectOptions = computed(() => toSelectOptions(options));
</script>
