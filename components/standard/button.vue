<template>
    <Button :icon="iconValue" :label="label" />
</template>

<script setup lang="ts">
    import { createOptional } from '@ti-platform/aide';
    import { Button } from 'primevue';
    import { computed } from 'vue';
    import type { Icon } from '~/utils/enums';

    const { label, icon } = defineProps<{
        label: string;
        icon?: Icon;
    }>();

    const iconValue = computed(() => createOptional(icon).map((v) => v.toString()).orUndefined());
</script>
