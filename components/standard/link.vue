<template>
    <NuxtLink :target="target" class="text-primary hover:underline"><slot>{{ textValue }}</slot></NuxtLink>
</template>

<script setup lang="ts">
    import { createOptional } from '@ti-platform/aide';
    import { computed } from 'vue';

    const { target = '_self', text } = defineProps<{
        text?: string;
        target?: '_blank' | '_self';
    }>();

    const textValue = computed(() => createOptional(text).orElse(''));
</script>
