<template>
    <AutoComplete
        v-model="internalModel"
        :multiple="multiple"
        :option-label="SELECT_OPTION_LABEL"
        :suggestions="toSelectOptions(filteredSuggestions)"
        fluid
        @blur="onBlur"
        @complete="onFilter"
        @before-hide="onHide"
        pt:input:type="search"
    />
</template>

<script setup lang="ts">
    import { AutoComplete } from 'primevue';
    import { ref } from 'vue';
    import { createOptional, MapPlus } from '@ti-platform/aide';
    import { syncRef } from '@vueuse/core';
    import type { AutoCompleteCompleteEvent } from 'primevue';
    import { SELECT_OPTION_LABEL, SELECT_OPTION_VALUE, toSelectOptions } from '~/utils/transformers';
    import type { SelectOption } from '~/utils/transformers';

    const { suggestions, forceSelection = false, multiple = false } = defineProps<{
        suggestions: MapPlus<string, string>;

        forceSelection?: boolean;
        multiple?: boolean;
    }>();

    const [model, modifiers] = defineModel<string | Array<string>>();

    const internalModel = ref<SelectOption | Array<SelectOption>>();
    const filteredSuggestions = ref<MapPlus<string, string>>(new MapPlus());
    const searchInput = ref<HTMLInputElement>();

    syncRef(model, internalModel, {
        transform: {
            ltr: (m) => {
                return createOptional(m)
                    .map((v) => (Array.isArray(v) ? v : [v]))
                    .map((v) => v.map(i => suggestions.has(i)
                        ? { [SELECT_OPTION_LABEL]: suggestions.getOrThrow(i), [SELECT_OPTION_VALUE]: i }
                        : { [SELECT_OPTION_LABEL]: i, [SELECT_OPTION_VALUE]: i }
                    ))
                    .map((v) => (multiple ? v : v.shift()))
                    .orUndefined();
            },

            rtl: (im) => {
                return createOptional(im)
                    .map((v) => (Array.isArray(v) ? v : [v]))
                    .map((v) => v.map((i) => i[SELECT_OPTION_VALUE]))
                    .map((v) => (multiple ? v : v.shift()))
                    .orUndefined();
            },
        },
    });

    function onBlur({ currentTarget }: Event) {
        createOptional(currentTarget).ifPresent((i) => (searchInput.value = i as HTMLInputElement));
    }

    function onFilter(event: AutoCompleteCompleteEvent) {
        const query = event.query.toUpperCase();
        filteredSuggestions.value = suggestions.filter(({ value }) => value.toUpperCase().includes(query));
    }

    function onHide() {
        if (!forceSelection) {
            return;
        }

        createOptional(searchInput.value)
            .map((i) => i.value.trim())
            .filter((v) => v !== '')
            .map((v) => (modifiers.caps ? v.toUpperCase() : v))
            .ifPresent((v) => {
                model.value = multiple ? [...createOptional(model.value).orElse([]), v] : v;
                searchInput.value!.value = '';
            });
    }
</script>
