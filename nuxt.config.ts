import <PERSON> from '@primeuix/themes/lara';
import tailwindcss from '@tailwindcss/vite';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
    compatibilityDate: '2025-05-15',
    devtools: { enabled: true },
    imports: { autoImport: false },
    modules: ['@primevue/nuxt-module'],
    components: [
        {
            path: '~/components',
            extensions: ['vue'],
        },
    ],
    vite: { plugins: [tailwindcss()] },
    primevue: {
        autoImport: false,
        options: { theme: { preset: <PERSON> } },
        components: {
            prefix: 'Prime',
            exclude: ['Editor', 'Chat', 'Form', 'FormField'],
        },
    },
});
