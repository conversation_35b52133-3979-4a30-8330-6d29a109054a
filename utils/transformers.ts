import { MapPlus, toMapPlus } from "@ti-platform/aide";

export const SELECT_OPTION_LABEL = 'l';
export const SELECT_OPTION_VALUE = 'v';

export type SelectOption = {
    [SELECT_OPTION_LABEL]: string;
    [SELECT_OPTION_VALUE]: string;
}

export function toSelectOptions(options: MapPlus<string, string> | Array<string>): Array<SelectOption> {
    return toMapPlus<string, string>(Array.isArray(options) ? options.map((i) => [i, i]) : options)
        .mapValues(({ key, value}) => ({ [SELECT_OPTION_LABEL]: value, [SELECT_OPTION_VALUE]: key }))
        .valuesArray();
}
